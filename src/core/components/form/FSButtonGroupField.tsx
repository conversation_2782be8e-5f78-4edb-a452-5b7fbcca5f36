import { useFieldContext } from "./form";

type Option = { 
  value: string; 
  label: string; 
  icon?: React.ReactNode;
};

interface Props {
  label: string;
  options: Option[];
}

export function FSButtonGroupField({ label, options }: Props) {
  const field = useFieldContext<string>();

  const isTouched = field.state.meta.isTouched;
  const errorsLength = field.state.meta.errors.length;
  const isError = isTouched && errorsLength;
  const errors = field.state.meta.errors;

  return (
    <fieldset className="fieldset">
      <legend className="fieldset-legend">{label}</legend>
      <div className="flex gap-2">
        {options.map((option) => {
          const isSelected = field.state.value === option.value;
          return (
            <button
              key={option.value}
              type="button"
              className={`btn flex-1 ${
                isSelected 
                  ? "btn-primary" 
                  : "btn-outline btn-neutral"
              }`}
              onClick={() => field.handleChange(option.value)}
            >
              {option.icon && (
                <span className="mr-2">{option.icon}</span>
              )}
              {option.label}
            </button>
          );
        })}
      </div>
      {isError
        ? errors.flatMap(({ message }) => (
            <p key={message} className="fieldset-label text-error">
              {message}
            </p>
          ))
        : null}
    </fieldset>
  );
}
